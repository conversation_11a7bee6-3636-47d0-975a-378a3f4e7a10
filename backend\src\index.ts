import { createClient } from '@supabase/supabase-js'
import * as dotenv from 'dotenv'
import { RouterOSAPI } from 'routeros-node';

// Define RouterOSClient as an alias for RouterOSAPI
type RouterOSClient = RouterOSAPI;

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_SERVICE_KEY in .env file')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

interface Subscriber {
  id?: string;
  display_name: string;
  username: string;
  password: string;
  type: string;
  phone: string | null;
  address: string | null;
  expires_at: string | null;
  is_connected: boolean;
  last_seen: string | null;
  notes: string | null;
  device_id: string;
  package_id: string | null;
  user_id: string;
  mikrotik_id: string;
}

interface Package {
  id?: string;
  name: string;
  type: string;
  duration_days: number;
  speed_download: string;
  speed_upload: string;
  mikrotik_profile: string;
  price: number | null;
  description: string | null;
  user_id: string;
  device_id: string;
}

interface Device {
  id: string;
  name: string;
  host: string;
  username: string;
  password: string;
  port: number;
  api_port: number;
  connection_type: string;
  is_active: boolean;
  last_sync_at: string | null;
  created_at: string;
  user_id: string;
}

async function main() {
  console.log('Backend started')

  const { data: devices, error } = await supabase
    .from('mikrotik_devices')
    .select('*')

  if (error) {
    console.error('Error fetching devices:', error)
    return
  }

  for (const device of devices as Device[]) {
    console.log(`Connecting to device: ${device.name}`)

    const client = new RouterOSAPI({
      host: device.host,
      user: device.username,
      password: device.password,
      port: device.port,
    });

    try {
      await client.connect();
      console.log(`Connected to device: ${device.name}`)

      await syncSubscribers(client, device.id, device.user_id);
      await syncPackages(client, device.id, device.user_id);

      await supabase.from('mikrotik_devices').update({ last_sync_at: new Date().toISOString(), is_active: true }).eq('id', device.id)

      await client.close();
    } catch (err) {
      console.error(`Error connecting to device: ${device.name}`, err)
      await supabase.from('mikrotik_devices').update({ is_active: false }).eq('id', device.id)
    }
  }
}

async function syncSubscribers(client: RouterOSClient, deviceId: string, userId: string) {
  const pppoeSecrets = await client.write('/ppp/secret/print');
  const hotspotUsers = await client.write('/ip/hotspot/user/print');

  const { data: packages, error: packagesError } = await supabase
    .from('packages')
    .select('id, name')
    .eq('device_id', deviceId);

  if (packagesError) {
    console.error('Error fetching packages:', packagesError);
    return;
  }

  const subscribers: Subscriber[] = [];

  for (const secret of pppoeSecrets) {
    const pkg = packages.find((p) => p.name === secret.profile);
    subscribers.push({
      display_name: secret.name,
      username: secret.name,
      password: secret.password,
      type: 'broadband',
      phone: null,
      address: null,
      expires_at: null,
      is_connected: secret.disabled === 'false',
      last_seen: null,
      notes: secret.comment,
      device_id: deviceId,
      package_id: pkg?.id || null,
      user_id: userId,
      mikrotik_id: secret['.id'],
    });
  }

  for (const user of hotspotUsers) {
    const pkg = packages.find((p) => p.name === user.profile);
    subscribers.push({
      display_name: user.name,
      username: user.name,
      password: user.password,
      type: 'hotspot',
      phone: null,
      address: null,
      expires_at: null,
      is_connected: user.disabled === 'false',
      last_seen: null,
      notes: user.comment,
      device_id: deviceId,
      package_id: pkg?.id || null,
      user_id: userId,
      mikrotik_id: user['.id'],
    });
  }

  const { error } = await supabase.from('subscribers').upsert(subscribers, { onConflict: 'mikrotik_id,device_id' });

  if (error) {
    console.error('Error upserting subscribers:', error);
  }
}

async function syncPackages(client: RouterOSClient, deviceId: string, userId: string) {
  const pppoeProfiles = await client.write('/ppp/profile/print');
  const hotspotProfiles = await client.write('/ip/hotspot/profile/print');

  const packages: Package[] = [];

  for (const profile of pppoeProfiles) {
    packages.push({
      name: profile.name,
      type: 'broadband',
      mikrotik_profile: profile.name,
      user_id: userId,
      device_id: deviceId,
      // These fields are not available in the profile, so we set them to default values
      duration_days: 30,
      speed_download: profile['rate-limit'] || '',
      speed_upload: profile['rate-limit'] || '',
      price: null,
      description: null,
    });
  }

  for (const profile of hotspotProfiles) {
    packages.push({
      name: profile.name,
      type: 'hotspot',
      mikrotik_profile: profile.name,
      user_id: userId,
      device_id: deviceId,
      // These fields are not available in the profile, so we set them to default values
      duration_days: 30,
      speed_download: profile['rate-limit'] || '',
      speed_upload: profile['rate-limit'] || '',
      price: null,
      description: null,
    });
  }

  const { error } = await supabase.from('packages').upsert(packages, { onConflict: 'name,device_id' });

  if (error) {
    console.error('Error upserting packages:', error);
  }
}

main()