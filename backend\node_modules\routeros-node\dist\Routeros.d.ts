/// <reference types="node" />
/// <reference types="node" />
import { Socket } from "node:net";
import { TLSSocket, ConnectionOptions } from "node:tls";
declare class Routeros {
    protected host: string;
    protected port: number;
    protected user: string;
    protected password: string;
    protected tlsOptions?: ConnectionOptions;
    protected timeout: number;
    protected socket: Socket | TLSSocket;
    constructor({ host, port, user, password, timeout, tlsOptions, }: {
        host: string;
        port: number;
        user: string;
        password: string;
        timeout?: number;
        tlsOptions?: ConnectionOptions;
    });
    private writeWords;
    private login;
    connect(): Promise<Omit<Routeros, "connect">>;
    write(queries: string[]): Promise<{
        [prop: string]: string;
    }[] | []>;
    destroy(): void;
}
export default Routeros;
