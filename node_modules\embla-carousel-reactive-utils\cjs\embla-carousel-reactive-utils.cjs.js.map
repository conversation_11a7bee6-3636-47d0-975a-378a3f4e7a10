{"version": 3, "file": "embla-carousel-reactive-utils.cjs.js", "sources": ["../src/components/utils.ts"], "sourcesContent": ["import { EmblaPluginType } from 'embla-carousel'\n\nexport function isObject(subject: unknown): subject is Record<string, unknown> {\n  return Object.prototype.toString.call(subject) === '[object Object]'\n}\n\nexport function isRecord(\n  subject: unknown\n): subject is Record<string | number, unknown> {\n  return isObject(subject) || Array.isArray(subject)\n}\n\nexport function canUseDOM(): boolean {\n  return !!(\n    typeof window !== 'undefined' &&\n    window.document &&\n    window.document.createElement\n  )\n}\n\nexport function areOptionsEqual(\n  optionsA: Record<string, unknown>,\n  optionsB: Record<string, unknown>\n): boolean {\n  const optionsAKeys = Object.keys(optionsA)\n  const optionsBKeys = Object.keys(optionsB)\n\n  if (optionsAKeys.length !== optionsBKeys.length) return false\n\n  const breakpointsA = JSON.stringify(Object.keys(optionsA.breakpoints || {}))\n  const breakpointsB = JSON.stringify(Object.keys(optionsB.breakpoints || {}))\n\n  if (breakpointsA !== breakpointsB) return false\n\n  return optionsAKeys.every((key) => {\n    const valueA = optionsA[key]\n    const valueB = optionsB[key]\n    if (typeof valueA === 'function') return `${valueA}` === `${valueB}`\n    if (!isRecord(valueA) || !isRecord(valueB)) return valueA === valueB\n    return areOptionsEqual(valueA, valueB)\n  })\n}\n\nexport function sortAndMapPluginToOptions(\n  plugins: EmblaPluginType[]\n): EmblaPluginType['options'][] {\n  return plugins\n    .concat()\n    .sort((a, b) => (a.name > b.name ? 1 : -1))\n    .map((plugin) => plugin.options)\n}\n\nexport function arePluginsEqual(\n  pluginsA: EmblaPluginType[],\n  pluginsB: EmblaPluginType[]\n): boolean {\n  if (pluginsA.length !== pluginsB.length) return false\n\n  const optionsA = sortAndMapPluginToOptions(pluginsA)\n  const optionsB = sortAndMapPluginToOptions(pluginsB)\n\n  return optionsA.every((optionA, index) => {\n    const optionB = optionsB[index]\n    return areOptionsEqual(optionA, optionB)\n  })\n}\n"], "names": ["isObject", "subject", "Object", "prototype", "toString", "call", "isRecord", "Array", "isArray", "canUseDOM", "window", "document", "createElement", "areOptionsEqual", "optionsA", "optionsB", "optionsAKeys", "keys", "optionsBKeys", "length", "breakpointsA", "JSON", "stringify", "breakpoints", "breakpointsB", "every", "key", "valueA", "valueB", "sortAndMapPluginToOptions", "plugins", "concat", "sort", "a", "b", "name", "map", "plugin", "options", "arePluginsEqual", "pluginsA", "pluginsB", "optionA", "index", "optionB"], "mappings": ";;AAEM,SAAUA,QAAQA,CAACC,OAAgB,EAAA;EACvC,OAAOC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,OAAO,CAAC,KAAK,iBAAiB;AACtE;AAEM,SAAUK,QAAQA,CACtBL,OAAgB,EAAA;EAEhB,OAAOD,QAAQ,CAACC,OAAO,CAAC,IAAIM,KAAK,CAACC,OAAO,CAACP,OAAO,CAAC;AACpD;SAEgBQ,SAASA,GAAA;AACvB,EAAA,OAAO,CAAC,EACN,OAAOC,MAAM,KAAK,WAAW,IAC7BA,MAAM,CAACC,QAAQ,IACfD,MAAM,CAACC,QAAQ,CAACC,aAAa,CAC9B;AACH;AAEgB,SAAAC,eAAeA,CAC7BC,QAAiC,EACjCC,QAAiC,EAAA;AAEjC,EAAA,MAAMC,YAAY,GAAGd,MAAM,CAACe,IAAI,CAACH,QAAQ,CAAC;AAC1C,EAAA,MAAMI,YAAY,GAAGhB,MAAM,CAACe,IAAI,CAACF,QAAQ,CAAC;EAE1C,IAAIC,YAAY,CAACG,MAAM,KAAKD,YAAY,CAACC,MAAM,EAAE,OAAO,KAAK;AAE7D,EAAA,MAAMC,YAAY,GAAGC,IAAI,CAACC,SAAS,CAACpB,MAAM,CAACe,IAAI,CAACH,QAAQ,CAACS,WAAW,IAAI,EAAE,CAAC,CAAC;AAC5E,EAAA,MAAMC,YAAY,GAAGH,IAAI,CAACC,SAAS,CAACpB,MAAM,CAACe,IAAI,CAACF,QAAQ,CAACQ,WAAW,IAAI,EAAE,CAAC,CAAC;AAE5E,EAAA,IAAIH,YAAY,KAAKI,YAAY,EAAE,OAAO,KAAK;AAE/C,EAAA,OAAOR,YAAY,CAACS,KAAK,CAAEC,GAAG,IAAI;AAChC,IAAA,MAAMC,MAAM,GAAGb,QAAQ,CAACY,GAAG,CAAC;AAC5B,IAAA,MAAME,MAAM,GAAGb,QAAQ,CAACW,GAAG,CAAC;AAC5B,IAAA,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE,OAAO,CAAGA,EAAAA,MAAM,CAAE,CAAA,KAAK,CAAGC,EAAAA,MAAM,CAAE,CAAA;AACpE,IAAA,IAAI,CAACtB,QAAQ,CAACqB,MAAM,CAAC,IAAI,CAACrB,QAAQ,CAACsB,MAAM,CAAC,EAAE,OAAOD,MAAM,KAAKC,MAAM;AACpE,IAAA,OAAOf,eAAe,CAACc,MAAM,EAAEC,MAAM,CAAC;AACxC,GAAC,CAAC;AACJ;AAEM,SAAUC,yBAAyBA,CACvCC,OAA0B,EAAA;AAE1B,EAAA,OAAOA,OAAO,CACXC,MAAM,EAAE,CACRC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAMD,CAAC,CAACE,IAAI,GAAGD,CAAC,CAACC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAE,CAAC,CAC1CC,GAAG,CAAEC,MAAM,IAAKA,MAAM,CAACC,OAAO,CAAC;AACpC;AAEgB,SAAAC,eAAeA,CAC7BC,QAA2B,EAC3BC,QAA2B,EAAA;EAE3B,IAAID,QAAQ,CAACrB,MAAM,KAAKsB,QAAQ,CAACtB,MAAM,EAAE,OAAO,KAAK;AAErD,EAAA,MAAML,QAAQ,GAAGe,yBAAyB,CAACW,QAAQ,CAAC;AACpD,EAAA,MAAMzB,QAAQ,GAAGc,yBAAyB,CAACY,QAAQ,CAAC;EAEpD,OAAO3B,QAAQ,CAACW,KAAK,CAAC,CAACiB,OAAO,EAAEC,KAAK,KAAI;AACvC,IAAA,MAAMC,OAAO,GAAG7B,QAAQ,CAAC4B,KAAK,CAAC;AAC/B,IAAA,OAAO9B,eAAe,CAAC6B,OAAO,EAAEE,OAAO,CAAC;AAC1C,GAAC,CAAC;AACJ;;;;;;;"}