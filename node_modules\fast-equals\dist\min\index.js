!function(r,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((r="undefined"!=typeof globalThis?globalThis:r||self)["fast-equals"]={})}(this,function(r){"use strict";var e=Object.getOwnPropertyNames,t=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty;function n(r,e){return function(t,a,n){return r(t,a,n)&&e(t,a,n)}}function u(r){return function(e,t,a){if(!e||!t||"object"!=typeof e||"object"!=typeof t)return r(e,t,a);var n=a.cache,u=n.get(e),o=n.get(t);if(u&&o)return u===t&&o===e;n.set(e,t),n.set(t,e);var i=r(e,t,a);return n.delete(e),n.delete(t),i}}function o(r){return e(r).concat(t(r))}var i=Object.hasOwn||function(r,e){return a.call(r,e)};function c(r,e){return r===e||!r&&!e&&r!=r&&e!=e}var l=Object.getOwnPropertyDescriptor,f=Object.keys;function s(r,e,t){var a=r.length;if(e.length!==a)return!1;for(;a-- >0;)if(!t.equals(r[a],e[a],a,a,r,e,t))return!1;return!0}function p(r,e){return c(r.getTime(),e.getTime())}function v(r,e){return r.name===e.name&&r.message===e.message&&r.cause===e.cause&&r.stack===e.stack}function q(r,e){return r===e}function E(r,e,t){var a=r.size;if(a!==e.size)return!1;if(!a)return!0;for(var n,u,o=new Array(a),i=r.entries(),c=0;(n=i.next())&&!n.done;){for(var l=e.entries(),f=!1,s=0;(u=l.next())&&!u.done;)if(o[s])s++;else{var p=n.value,v=u.value;if(t.equals(p[0],v[0],c,s,r,e,t)&&t.equals(p[1],v[1],p[0],v[0],r,e,t)){f=o[s]=!0;break}s++}if(!f)return!1;c++}return!0}var b=c;function m(r,e,t){var a=f(r),n=a.length;if(f(e).length!==n)return!1;for(;n-- >0;)if(!O(r,e,t,a[n]))return!1;return!0}function y(r,e,t){var a,n,u,i=o(r),c=i.length;if(o(e).length!==c)return!1;for(;c-- >0;){if(!O(r,e,t,a=i[c]))return!1;if(n=l(r,a),u=l(e,a),(n||u)&&(!n||!u||n.configurable!==u.configurable||n.enumerable!==u.enumerable||n.writable!==u.writable))return!1}return!0}function g(r,e){return c(r.valueOf(),e.valueOf())}function h(r,e){return r.source===e.source&&r.flags===e.flags}function d(r,e,t){var a=r.size;if(a!==e.size)return!1;if(!a)return!0;for(var n,u,o=new Array(a),i=r.values();(n=i.next())&&!n.done;){for(var c=e.values(),l=!1,f=0;(u=c.next())&&!u.done;){if(!o[f]&&t.equals(n.value,u.value,n.value,u.value,r,e,t)){l=o[f]=!0;break}f++}if(!l)return!1}return!0}function j(r,e){var t=r.length;if(e.length!==t)return!1;for(;t-- >0;)if(r[t]!==e[t])return!1;return!0}function w(r,e){return r.hostname===e.hostname&&r.pathname===e.pathname&&r.protocol===e.protocol&&r.port===e.port&&r.hash===e.hash&&r.username===e.username&&r.password===e.password}function O(r,e,t,a){return!("_owner"!==a&&"__o"!==a&&"__v"!==a||!r.$$typeof&&!e.$$typeof)||i(e,a)&&t.equals(r[a],e[a],a,a,r,e,t)}var S=Array.isArray,A="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,C=Object.assign,x=Object.prototype.toString.call.bind(Object.prototype.toString);function k(r){var e=r.areArraysEqual,t=r.areDatesEqual,a=r.areErrorsEqual,n=r.areFunctionsEqual,u=r.areMapsEqual,o=r.areNumbersEqual,i=r.areObjectsEqual,c=r.arePrimitiveWrappersEqual,l=r.areRegExpsEqual,f=r.areSetsEqual,s=r.areTypedArraysEqual,p=r.areUrlsEqual,v=r.unknownTagComparators;return function(r,q,E){if(r===q)return!0;if(null==r||null==q)return!1;var b=typeof r;if(b!==typeof q)return!1;if("object"!==b)return"number"===b?o(r,q,E):"function"===b&&n(r,q,E);var m=r.constructor;if(m!==q.constructor)return!1;if(m===Object)return i(r,q,E);if(S(r))return e(r,q,E);if(null!=A&&A(r))return s(r,q,E);if(m===Date)return t(r,q,E);if(m===RegExp)return l(r,q,E);if(m===Map)return u(r,q,E);if(m===Set)return f(r,q,E);var y,g=x(r);if("[object Date]"===g)return t(r,q,E);if("[object RegExp]"===g)return l(r,q,E);if("[object Map]"===g)return u(r,q,E);if("[object Set]"===g)return f(r,q,E);if("[object Object]"===g)return"function"!=typeof r.then&&"function"!=typeof q.then&&i(r,q,E);if("[object URL]"===g)return p(r,q,E);if("[object Error]"===g)return a(r,q,E);if("[object Arguments]"===g)return i(r,q,E);if("[object Boolean]"===g||"[object Number]"===g||"[object String]"===g)return c(r,q,E);if(v){var h=v[g];if(!h){var d=null!=(y=r)?y[Symbol.toStringTag]:void 0;d&&(h=v[d])}if(h)return h(r,q,E)}return!1}}var T=B(),D=B({strict:!0}),M=B({circular:!0}),P=B({circular:!0,strict:!0}),I=B({createInternalComparator:function(){return c}}),R=B({strict:!0,createInternalComparator:function(){return c}}),_=B({circular:!0,createInternalComparator:function(){return c}}),z=B({circular:!0,createInternalComparator:function(){return c},strict:!0});function B(r){void 0===r&&(r={});var e,t=r.circular,a=void 0!==t&&t,o=r.createInternalComparator,i=r.createState,c=r.strict,l=void 0!==c&&c,f=function(r){var e=r.circular,t=r.createCustomConfig,a=r.strict,o={areArraysEqual:a?y:s,areDatesEqual:p,areErrorsEqual:v,areFunctionsEqual:q,areMapsEqual:a?n(E,y):E,areNumbersEqual:b,areObjectsEqual:a?y:m,arePrimitiveWrappersEqual:g,areRegExpsEqual:h,areSetsEqual:a?n(d,y):d,areTypedArraysEqual:a?y:j,areUrlsEqual:w,unknownTagComparators:void 0};if(t&&(o=C({},o,t(o))),e){var i=u(o.areArraysEqual),c=u(o.areMapsEqual),l=u(o.areObjectsEqual),f=u(o.areSetsEqual);o=C({},o,{areArraysEqual:i,areMapsEqual:c,areObjectsEqual:l,areSetsEqual:f})}return o}(r),O=k(f);return function(r){var e=r.circular,t=r.comparator,a=r.createState,n=r.equals,u=r.strict;if(a)return function(r,o){var i=a(),c=i.cache,l=void 0===c?e?new WeakMap:void 0:c,f=i.meta;return t(r,o,{cache:l,equals:n,meta:f,strict:u})};if(e)return function(r,e){return t(r,e,{cache:new WeakMap,equals:n,meta:void 0,strict:u})};var o={cache:void 0,equals:n,meta:void 0,strict:u};return function(r,e){return t(r,e,o)}}({circular:a,comparator:O,createState:i,equals:o?o(O):(e=O,function(r,t,a,n,u,o,i){return e(r,t,i)}),strict:l})}r.circularDeepEqual=M,r.circularShallowEqual=_,r.createCustomEqual=B,r.deepEqual=T,r.sameValueZeroEqual=c,r.shallowEqual=I,r.strictCircularDeepEqual=P,r.strictCircularShallowEqual=z,r.strictDeepEqual=D,r.strictShallowEqual=R});
