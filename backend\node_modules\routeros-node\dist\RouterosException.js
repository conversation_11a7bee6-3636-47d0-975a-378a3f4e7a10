"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
class RouterosException extends Error {
    constructor(message) {
        super(message);
        this.name = this.constructor.name;
        this.message = message;
        const actualProto = new.target.prototype;
        if (Object.setPrototypeOf) {
            Object.setPrototypeOf(this, actualProto);
        }
        else {
            this.__proto__ = actualProto;
        }
    }
}
exports.default = RouterosException;
