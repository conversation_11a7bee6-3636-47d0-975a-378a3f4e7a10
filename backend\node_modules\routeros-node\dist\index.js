"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RouterosException = exports.Routeros = void 0;
const Routeros_1 = __importDefault(require("./Routeros"));
exports.Routeros = Routeros_1.default;
const RouterosException_1 = __importDefault(require("./RouterosException"));
exports.RouterosException = RouterosException_1.default;
