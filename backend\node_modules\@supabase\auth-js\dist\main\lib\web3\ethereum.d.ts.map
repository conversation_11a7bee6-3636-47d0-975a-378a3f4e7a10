{"version": 3, "file": "ethereum.d.ts", "sourceRoot": "", "sources": ["../../../../src/lib/web3/ethereum.ts"], "names": [], "mappings": "AAEA,oBAAY,GAAG,GAAG,KAAK,MAAM,EAAE,CAAA;AAE/B,oBAAY,OAAO,GAAG,GAAG,CAAA;AAEzB,oBAAY,eAAe,GAAG;IAC5B,eAAe,CAAC,QAAQ,EAAE,OAAO,EAAE,GAAG,IAAI,CAAA;IAC1C,YAAY,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,CAAA;IACnC,OAAO,CAAC,WAAW,EAAE;QAAE,OAAO,EAAE,MAAM,CAAA;KAAE,GAAG,IAAI,CAAA;IAC/C,UAAU,CAAC,KAAK,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,MAAM,CAAA;KAAE,GAAG,IAAI,CAAA;IAC1D,OAAO,CAAC,OAAO,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,OAAO,CAAA;KAAE,GAAG,IAAI,CAAA;CACxD,CAAA;AAED,oBAAY,aAAa,GAAG;IAC1B,EAAE,CAAC,KAAK,SAAS,MAAM,eAAe,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,eAAe,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;IAC7F,cAAc,CAAC,KAAK,SAAS,MAAM,eAAe,EAChD,KAAK,EAAE,KAAK,EACZ,QAAQ,EAAE,eAAe,CAAC,KAAK,CAAC,GAC/B,IAAI,CAAA;CACR,CAAA;AAED,oBAAY,gBAAgB,GAAG,CAAC,IAAI,EAAE;IAAE,MAAM,EAAE,MAAM,CAAC;IAAC,MAAM,CAAC,EAAE,OAAO,CAAA;CAAE,KAAK,OAAO,CAAC,OAAO,CAAC,CAAA;AAE/F,oBAAY,eAAe,GAAG,aAAa,GAAG;IAC5C,OAAO,EAAE,MAAM,CAAA;IACf,OAAO,EAAE,gBAAgB,CAAA;CAC1B,CAAA;AAED,oBAAY,cAAc,GAAG,eAAe,CAAA;AAE5C;;GAEG;AACH,oBAAY,WAAW,GAAG;IACxB;;OAEG;IACH,OAAO,EAAE,OAAO,CAAA;IAChB;;OAEG;IACH,OAAO,EAAE,MAAM,CAAA;IACf;;OAEG;IACH,MAAM,EAAE,MAAM,CAAA;IACd;;OAEG;IACH,cAAc,CAAC,EAAE,IAAI,GAAG,SAAS,CAAA;IACjC;;OAEG;IACH,QAAQ,CAAC,EAAE,IAAI,GAAG,SAAS,CAAA;IAC3B;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAA;IACd;;OAEG;IACH,SAAS,CAAC,EAAE,IAAI,GAAG,SAAS,CAAA;IAC5B;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC9B;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,EAAE,GAAG,SAAS,CAAA;IAChC;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC3B;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC9B;;OAEG;IACH,GAAG,EAAE,MAAM,CAAA;IACX;;OAEG;IACH,OAAO,EAAE,GAAG,CAAA;CACb,CAAA;AAED,oBAAY,mBAAmB,GAAG,WAAW,CAAA;AAE7C,wBAAgB,UAAU,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAKnD;AAED,wBAAgB,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,MAAM,CAExC;AAED,wBAAgB,KAAK,CAAC,KAAK,EAAE,MAAM,GAAG,GAAG,CAIxC;AAED;;GAEG;AACH,wBAAgB,iBAAiB,CAAC,UAAU,EAAE,WAAW,GAAG,MAAM,CAwEjE"}