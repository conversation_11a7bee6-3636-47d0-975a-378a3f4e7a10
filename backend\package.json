{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "ts-node src/index.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@supabase/supabase-js": "^2.43.4", "dotenv": "^16.4.5", "routeros-node": "^0.1.0"}, "devDependencies": {"@types/node": "^20.12.12", "ts-node": "^10.9.2", "typescript": "^5.4.5"}}