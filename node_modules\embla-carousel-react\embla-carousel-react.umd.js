!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define(["react"],t):(n="undefined"!=typeof globalThis?globalThis:n||self).EmblaCarouselReact=t(n.React)}(this,(function(n){"use strict";function t(n){return function(n){return"[object Object]"===Object.prototype.toString.call(n)}(n)||Array.isArray(n)}function e(n,r){const o=Object.keys(n),i=Object.keys(r);if(o.length!==i.length)return!1;return JSON.stringify(Object.keys(n.breakpoints||{}))===JSON.stringify(Object.keys(r.breakpoints||{}))&&o.every((o=>{const i=n[o],c=r[o];return"function"==typeof i?`${i}`==`${c}`:t(i)&&t(c)?e(i,c):i===c}))}function r(n){return n.concat().sort(((n,t)=>n.name>t.name?1:-1)).map((n=>n.options))}function o(n){return"number"==typeof n}function i(n){return"string"==typeof n}function c(n){return"boolean"==typeof n}function u(n){return"[object Object]"===Object.prototype.toString.call(n)}function s(n){return Math.abs(n)}function a(n){return Math.sign(n)}function d(n,t){return s(n-t)}function f(n){return h(n).map(Number)}function l(n){return n[p(n)]}function p(n){return Math.max(0,n.length-1)}function g(n,t){return t===p(n)}function m(n,t=0){return Array.from(Array(n),((n,e)=>t+e))}function h(n){return Object.keys(n)}function y(n,t){return[n,t].reduce(((n,t)=>(h(t).forEach((e=>{const r=n[e],o=t[e],i=u(r)&&u(o);n[e]=i?y(r,o):o})),n)),{})}function x(n,t){return void 0!==t.MouseEvent&&n instanceof t.MouseEvent}function b(){let n=[];const t={add:function(e,r,o,i={passive:!0}){let c;if("addEventListener"in e)e.addEventListener(r,o,i),c=()=>e.removeEventListener(r,o,i);else{const n=e;n.addListener(o),c=()=>n.removeListener(o)}return n.push(c),t},clear:function(){n=n.filter((n=>n()))}};return t}function v(n,t,e,r){const o=b(),i=1e3/60;let c=null,u=0,s=0;function a(n){if(!s)return;c||(c=n,e(),e());const o=n-c;for(c=n,u+=o;u>=i;)e(),u-=i;r(u/i),s&&(s=t.requestAnimationFrame(a))}function d(){t.cancelAnimationFrame(s),c=null,u=0,s=0}return{init:function(){o.add(n,"visibilitychange",(()=>{n.hidden&&(c=null,u=0)}))},destroy:function(){d(),o.clear()},start:function(){s||(s=t.requestAnimationFrame(a))},stop:d,update:e,render:r}}function S(n=0,t=0){const e=s(n-t);function r(t){return t<n}function o(n){return n>t}function i(n){return r(n)||o(n)}return{length:e,max:t,min:n,constrain:function(e){return i(e)?r(e)?n:t:e},reachedAny:i,reachedMax:o,reachedMin:r,removeOffset:function(n){return e?n-e*Math.ceil((n-t)/e):n}}}function w(n,t,e){const{constrain:r}=S(0,n),o=n+1;let i=c(t);function c(n){return e?s((o+n)%o):r(n)}function u(){return i}function a(){return w(n,u(),e)}const d={get:u,set:function(n){return i=c(n),d},add:function(n){return a().set(u()+n)},clone:a};return d}function E(n,t,e,r,o,i,u,f,l,p,g,m,h,y,v,w,E,L,O){const{cross:D,direction:I}=n,A=["INPUT","SELECT","TEXTAREA"],M={passive:!1},F=b(),T=b(),k=S(50,225).constrain(y.measure(20)),P={mouse:300,touch:400},z={mouse:500,touch:600},H=v?43:25;let j=!1,R=0,V=0,B=!1,C=!1,N=!1,q=!1;function G(n){if(!x(n,r)&&n.touches.length>=2)return $(n);const t=i.readPoint(n),e=i.readPoint(n,D),c=d(t,R),u=d(e,V);if(!C&&!q){if(!n.cancelable)return $(n);if(C=c>u,!C)return $(n)}const s=i.pointerMove(n);c>w&&(N=!0),p.useFriction(.3).useDuration(.75),f.start(),o.add(I(s)),n.preventDefault()}function $(n){const t=g.byDistance(0,!1).index!==m.get(),e=i.pointerUp(n)*(v?z:P)[q?"mouse":"touch"],r=function(n,t){const e=m.add(-1*a(n)),r=g.byDistance(n,!v).distance;return v||s(n)<k?r:E&&t?.5*r:g.byIndex(e.get(),0).distance}(I(e),t),o=function(n,t){if(0===n||0===t)return 0;if(s(n)<=s(t))return 0;const e=d(s(n),s(t));return s(e/n)}(e,r),c=H-10*o,u=L+o/50;C=!1,B=!1,T.clear(),p.useDuration(c).useFriction(u),l.distance(r,!v),q=!1,h.emit("pointerUp")}function U(n){N&&(n.stopPropagation(),n.preventDefault(),N=!1)}return{init:function(n){if(!O)return;function s(s){(c(O)||O(n,s))&&function(n){const c=x(n,r);if(q=c,N=v&&c&&!n.buttons&&j,j=d(o.get(),u.get())>=2,c&&0!==n.button)return;if(function(n){const t=n.nodeName||"";return A.includes(t)}(n.target))return;B=!0,i.pointerDown(n),p.useFriction(0).useDuration(0),o.set(u),function(){const n=q?e:t;T.add(n,"touchmove",G,M).add(n,"touchend",$).add(n,"mousemove",G,M).add(n,"mouseup",$)}(),R=i.readPoint(n),V=i.readPoint(n,D),h.emit("pointerDown")}(s)}const a=t;F.add(a,"dragstart",(n=>n.preventDefault()),M).add(a,"touchmove",(()=>{}),M).add(a,"touchend",(()=>{})).add(a,"touchstart",s).add(a,"mousedown",s).add(a,"touchcancel",$).add(a,"contextmenu",$).add(a,"click",U,!0)},destroy:function(){F.clear(),T.clear()},pointerDown:function(){return B}}}function L(n,t){let e,r;function o(n){return n.timeStamp}function i(e,r){const o="client"+("x"===(r||n.scroll)?"X":"Y");return(x(e,t)?e:e.touches[0])[o]}return{pointerDown:function(n){return e=n,r=n,i(n)},pointerMove:function(n){const t=i(n)-i(r),c=o(n)-o(e)>170;return r=n,c&&(e=n),t},pointerUp:function(n){if(!e||!r)return 0;const t=i(r)-i(e),c=o(n)-o(e),u=o(n)-o(r)>170,a=t/c;return c&&!u&&s(a)>.1?a:0},readPoint:i}}function O(n,t,e,r,o,i,u){const a=[n].concat(r);let d,f,l=[],p=!1;function g(n){return o.measureSize(u.measure(n))}return{init:function(o){i&&(f=g(n),l=r.map(g),d=new ResizeObserver((e=>{(c(i)||i(o,e))&&function(e){for(const i of e){if(p)return;const e=i.target===n,c=r.indexOf(i.target),u=e?f:l[c];if(s(g(e?n:r[c])-u)>=.5){o.reInit(),t.emit("resize");break}}}(e)})),e.requestAnimationFrame((()=>{a.forEach((n=>d.observe(n)))})))},destroy:function(){p=!0,d&&d.disconnect()}}}function D(n,t,e,r,o){const i=o.measure(10),c=o.measure(50),u=S(.1,.99);let a=!1;function d(){return!a&&(!!n.reachedAny(e.get())&&!!n.reachedAny(t.get()))}return{shouldConstrain:d,constrain:function(o){if(!d())return;const a=n.reachedMin(t.get())?"min":"max",f=s(n[a]-t.get()),l=e.get()-t.get(),p=u.constrain(f/c);e.subtract(l*p),!o&&s(l)<i&&(e.set(n.constrain(e.get())),r.useDuration(25).useBaseFriction())},toggleActive:function(n){a=!n}}}function I(n,t,e,r){const o=t.min+.1,i=t.max+.1,{reachedMin:c,reachedMax:u}=S(o,i);return{loop:function(t){if(!function(n){return 1===n?u(e.get()):-1===n&&c(e.get())}(t))return;const o=n*(-1*t);r.forEach((n=>n.add(o)))}}}function A(n,t,e,r,o){const{reachedAny:i,removeOffset:c,constrain:u}=r;function d(n){return n.concat().sort(((n,t)=>s(n)-s(t)))[0]}function f(t,r){const o=[t,t+e,t-e];if(!n)return t;if(!r)return d(o);const i=o.filter((n=>a(n)===r));return i.length?d(i):l(o)-e}return{byDistance:function(e,r){const a=o.get()+e,{index:d,distance:l}=function(e){const r=n?c(e):u(e),o=t.map(((n,t)=>({diff:f(n-r,0),index:t}))).sort(((n,t)=>s(n.diff)-s(t.diff))),{index:i}=o[0];return{index:i,distance:r}}(a),p=!n&&i(a);return!r||p?{index:d,distance:e}:{index:d,distance:e+f(t[d]-l,0)}},byIndex:function(n,e){return{index:n,distance:f(t[n]-o.get(),e)}},shortcut:f}}function M(n,t,e,r,i,u,s,a){const d={passive:!0,capture:!0};let f=0;function l(n){"Tab"===n.code&&(f=(new Date).getTime())}return{init:function(p){a&&(u.add(document,"keydown",l,!1),t.forEach(((t,l)=>{u.add(t,"focus",(t=>{(c(a)||a(p,t))&&function(t){if((new Date).getTime()-f>10)return;s.emit("slideFocusStart"),n.scrollLeft=0;const c=e.findIndex((n=>n.includes(t)));o(c)&&(i.useDuration(0),r.index(c,0),s.emit("slideFocus"))}(l)}),d)})))}}}function F(n){let t=n;function e(n){return o(n)?n:n.get()}return{get:function(){return t},set:function(n){t=e(n)},add:function(n){t+=e(n)},subtract:function(n){t-=e(n)}}}function T(n,t){const e="x"===n.scroll?function(n){return`translate3d(${n}px,0px,0px)`}:function(n){return`translate3d(0px,${n}px,0px)`},r=t.style;let o=null,i=!1;return{clear:function(){i||(r.transform="",t.getAttribute("style")||t.removeAttribute("style"))},to:function(t){if(i)return;const c=(u=n.direction(t),Math.round(100*u)/100);var u;c!==o&&(r.transform=e(c),o=c)},toggleActive:function(n){i=!n}}}function k(n,t,e,r,o,i,c,u,s){const a=.5,d=f(o),l=f(o).reverse(),p=function(){const n=c[0];return h(m(l,n),e,!1)}().concat(function(){const n=t-c[0]-1;return h(m(d,n),-e,!0)}());function g(n,t){return n.reduce(((n,t)=>n-o[t]),t)}function m(n,t){return n.reduce(((n,e)=>g(n,t)>0?n.concat([e]):n),[])}function h(o,c,d){const f=function(n){return i.map(((e,o)=>({start:e-r[o]+a+n,end:e+t-a+n})))}(c);return o.map((t=>{const r=d?0:-e,o=d?e:0,i=d?"end":"start",c=f[t][i];return{index:t,loopPoint:c,slideLocation:F(-1),translate:T(n,s[t]),target:()=>u.get()>c?r:o}}))}return{canLoop:function(){return p.every((({index:n})=>g(d.filter((t=>t!==n)),t)<=.1))},clear:function(){p.forEach((n=>n.translate.clear()))},loop:function(){p.forEach((n=>{const{target:t,translate:e,slideLocation:r}=n,o=t();o!==r.get()&&(e.to(o),r.set(o))}))},loopPoints:p}}function P(n,t,e){let r,o=!1;return{init:function(i){e&&(r=new MutationObserver((n=>{o||(c(e)||e(i,n))&&function(n){for(const e of n)if("childList"===e.type){i.reInit(),t.emit("slidesChanged");break}}(n)})),r.observe(n,{childList:!0}))},destroy:function(){r&&r.disconnect(),o=!0}}}function z(n,t,e,r){const o={};let i,c=null,u=null,s=!1;return{init:function(){i=new IntersectionObserver((n=>{s||(n.forEach((n=>{const e=t.indexOf(n.target);o[e]=n})),c=null,u=null,e.emit("slidesInView"))}),{root:n.parentElement,threshold:r}),t.forEach((n=>i.observe(n)))},destroy:function(){i&&i.disconnect(),s=!0},get:function(n=!0){if(n&&c)return c;if(!n&&u)return u;const t=function(n){return h(o).reduce(((t,e)=>{const r=parseInt(e),{isIntersecting:i}=o[r];return(n&&i||!n&&!i)&&t.push(r),t}),[])}(n);return n&&(c=t),n||(u=t),t}}}function H(n,t,e,r,i,c,u,a,d){const{startEdge:g,endEdge:m,direction:h}=n,y=o(e);return{groupSlides:function(n){return y?function(n,t){return f(n).filter((n=>n%t==0)).map((e=>n.slice(e,e+t)))}(n,e):function(n){return n.length?f(n).reduce(((e,o,f)=>{const y=l(e)||0,x=0===y,b=o===p(n),v=i[g]-c[y][g],S=i[g]-c[o][m],w=!r&&x?h(u):0,E=s(S-(!r&&b?h(a):0)-(v+w));return f&&E>t+d&&e.push(o),b&&e.push(n.length),e}),[]).map(((t,e,r)=>{const o=Math.max(r[e-1]||0);return n.slice(o,t)})):[]}(n)}}}function j(n,t,e,r,o,c,u){const{align:h,axis:y,direction:x,startIndex:j,loop:R,duration:V,dragFree:B,dragThreshold:C,inViewThreshold:N,slidesToScroll:q,skipSnaps:G,containScroll:$,watchResize:U,watchSlides:W,watchDrag:J,watchFocus:Q}=c,X={measure:function(n){const{offsetTop:t,offsetLeft:e,offsetWidth:r,offsetHeight:o}=n;return{top:t,right:e+r,bottom:t+o,left:e,width:r,height:o}}},Y=X.measure(t),K=e.map(X.measure),Z=function(n,t){const e="rtl"===t,r="y"===n,o=!r&&e?-1:1;return{scroll:r?"y":"x",cross:r?"x":"y",startEdge:r?"top":e?"right":"left",endEdge:r?"bottom":e?"left":"right",measureSize:function(n){const{height:t,width:e}=n;return r?t:e},direction:function(n){return n*o}}}(y,x),_=Z.measureSize(Y),nn=function(n){return{measure:function(t){return n*(t/100)}}}(_),tn=function(n,t){const e={start:function(){return 0},center:function(n){return r(n)/2},end:r};function r(n){return t-n}return{measure:function(r,o){return i(n)?e[n](r):n(t,r,o)}}}(h,_),en=!R&&!!$,rn=R||!!$,{slideSizes:on,slideSizesWithGaps:cn,startGap:un,endGap:sn}=function(n,t,e,r,o,i){const{measureSize:c,startEdge:u,endEdge:a}=n,d=e[0]&&o,f=function(){if(!d)return 0;const n=e[0];return s(t[u]-n[u])}(),p=function(){if(!d)return 0;const n=i.getComputedStyle(l(r));return parseFloat(n.getPropertyValue(`margin-${a}`))}(),m=e.map(c),h=e.map(((n,t,e)=>{const r=!t,o=g(e,t);return r?m[t]+f:o?m[t]+p:e[t+1][u]-n[u]})).map(s);return{slideSizes:m,slideSizesWithGaps:h,startGap:f,endGap:p}}(Z,Y,K,e,rn,o),an=H(Z,_,q,R,Y,K,un,sn,2),{snaps:dn,snapsAligned:fn}=function(n,t,e,r,o){const{startEdge:i,endEdge:c}=n,{groupSlides:u}=o,a=u(r).map((n=>l(n)[c]-n[0][i])).map(s).map(t.measure),d=r.map((n=>e[i]-n[i])).map((n=>-s(n))),f=u(d).map((n=>n[0])).map(((n,t)=>n+a[t]));return{snaps:d,snapsAligned:f}}(Z,tn,Y,K,an),ln=-l(dn)+l(cn),{snapsContained:pn,scrollContainLimit:gn}=function(n,t,e,r,o){const i=S(-t+n,0),c=e.map(((n,t)=>{const{min:r,max:o}=i,c=i.constrain(n),u=!t,a=g(e,t);return u?o:a||s(r,c)?r:s(o,c)?o:c})).map((n=>parseFloat(n.toFixed(3)))),u=function(){const n=c[0],t=l(c);return S(c.lastIndexOf(n),c.indexOf(t)+1)}();function s(n,t){return d(n,t)<=1}return{snapsContained:function(){if(t<=n+o)return[i.max];if("keepSnaps"===r)return c;const{min:e,max:s}=u;return c.slice(e,s)}(),scrollContainLimit:u}}(_,ln,fn,$,2),mn=en?pn:fn,{limit:hn}=function(n,t,e){const r=t[0];return{limit:S(e?r-n:l(t),r)}}(ln,mn,R),yn=w(p(mn),j,R),xn=yn.clone(),bn=f(e),vn=v(r,o,(()=>(({dragHandler:n,scrollBody:t,scrollBounds:e,options:{loop:r}})=>{r||e.constrain(n.pointerDown()),t.seek()})(zn)),(n=>(({scrollBody:n,translate:t,location:e,offsetLocation:r,previousLocation:o,scrollLooper:i,slideLooper:c,dragHandler:u,animation:s,eventHandler:a,scrollBounds:d,options:{loop:f}},l)=>{const p=n.settled(),g=!d.shouldConstrain(),m=f?p:p&&g,h=m&&!u.pointerDown();h&&s.stop();const y=e.get()*l+o.get()*(1-l);r.set(y),f&&(i.loop(n.direction()),c.loop()),t.to(r.get()),h&&a.emit("settle"),m||a.emit("scroll")})(zn,n))),Sn=mn[yn.get()],wn=F(Sn),En=F(Sn),Ln=F(Sn),On=F(Sn),Dn=function(n,t,e,r,o,i){let c=0,u=0,d=o,f=i,l=n.get(),p=0;function g(n){return d=n,h}function m(n){return f=n,h}const h={direction:function(){return u},duration:function(){return d},velocity:function(){return c},seek:function(){const t=r.get()-n.get();let o=0;return d?(e.set(n),c+=t/d,c*=f,l+=c,n.add(c),o=l-p):(c=0,e.set(r),n.set(r),o=t),u=a(o),p=l,h},settled:function(){return s(r.get()-t.get())<.001},useBaseFriction:function(){return m(i)},useBaseDuration:function(){return g(o)},useFriction:m,useDuration:g};return h}(wn,Ln,En,On,V,.68),In=A(R,mn,ln,hn,On),An=function(n,t,e,r,o,i,c){function u(o){const u=o.distance,s=o.index!==t.get();i.add(u),u&&(r.duration()?n.start():(n.update(),n.render(1),n.update())),s&&(e.set(t.get()),t.set(o.index),c.emit("select"))}return{distance:function(n,t){u(o.byDistance(n,t))},index:function(n,e){const r=t.clone().set(n);u(o.byIndex(r.get(),e))}}}(vn,yn,xn,Dn,In,On,u),Mn=function(n){const{max:t,length:e}=n;return{get:function(n){return e?(n-t)/-e:0}}}(hn),Fn=b(),Tn=z(t,e,u,N),{slideRegistry:kn}=function(n,t,e,r,o,i){const{groupSlides:c}=o,{min:u,max:s}=r;return{slideRegistry:function(){const r=c(i),o=!n||"keepSnaps"===t;return 1===e.length?[i]:o?r:r.slice(u,s).map(((n,t,e)=>{const r=!t,o=g(e,t);return r?m(l(e[0])+1):o?m(p(i)-l(e)[0]+1,l(e)[0]):n}))}()}}(en,$,mn,gn,an,bn),Pn=M(n,e,kn,An,Dn,Fn,u,Q),zn={ownerDocument:r,ownerWindow:o,eventHandler:u,containerRect:Y,slideRects:K,animation:vn,axis:Z,dragHandler:E(Z,n,r,o,On,L(Z,o),wn,vn,An,Dn,In,yn,u,nn,B,C,G,.68,J),eventStore:Fn,percentOfView:nn,index:yn,indexPrevious:xn,limit:hn,location:wn,offsetLocation:Ln,previousLocation:En,options:c,resizeHandler:O(t,u,o,e,Z,U,X),scrollBody:Dn,scrollBounds:D(hn,Ln,On,Dn,nn),scrollLooper:I(ln,hn,Ln,[wn,Ln,En,On]),scrollProgress:Mn,scrollSnapList:mn.map(Mn.get),scrollSnaps:mn,scrollTarget:In,scrollTo:An,slideLooper:k(Z,_,ln,on,cn,dn,mn,Ln,e),slideFocus:Pn,slidesHandler:P(t,u,W),slidesInView:Tn,slideIndexes:bn,slideRegistry:kn,slidesToScroll:an,target:On,translate:T(Z,t)};return zn}const R={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function V(n){function t(n,t){return y(n,t||{})}const e={mergeOptions:t,optionsAtMedia:function(e){const r=e.breakpoints||{},o=h(r).filter((t=>n.matchMedia(t).matches)).map((n=>r[n])).reduce(((n,e)=>t(n,e)),{});return t(e,o)},optionsMediaQueries:function(t){return t.map((n=>h(n.breakpoints||{}))).reduce(((n,t)=>n.concat(t)),[]).map(n.matchMedia)}};return e}function B(n,t,e){const r=n.ownerDocument,o=r.defaultView,c=V(o),u=function(n){let t=[];return{init:function(e,r){return t=r.filter((({options:t})=>!1!==n.optionsAtMedia(t).active)),t.forEach((t=>t.init(e,n))),r.reduce(((n,t)=>Object.assign(n,{[t.name]:t})),{})},destroy:function(){t=t.filter((n=>n.destroy()))}}}(c),s=b(),a=function(){let n,t={};function e(n){return t[n]||[]}const r={init:function(t){n=t},emit:function(t){return e(t).forEach((e=>e(n,t))),r},off:function(n,o){return t[n]=e(n).filter((n=>n!==o)),r},on:function(n,o){return t[n]=e(n).concat([o]),r},clear:function(){t={}}};return r}(),{mergeOptions:d,optionsAtMedia:f,optionsMediaQueries:l}=c,{on:p,off:g,emit:m}=a,h=A;let y,x,v,S,w=!1,E=d(R,B.globalOptions),L=d(E),O=[];function D(t){const e=j(n,v,S,r,o,t,a);if(t.loop&&!e.slideLooper.canLoop()){return D(Object.assign({},t,{loop:!1}))}return e}function I(t,e){w||(E=d(E,t),L=f(E),O=e||O,function(){const{container:t,slides:e}=L,r=i(t)?n.querySelector(t):t;v=r||n.children[0];const o=i(e)?v.querySelectorAll(e):e;S=[].slice.call(o||v.children)}(),y=D(L),l([E,...O.map((({options:n})=>n))]).forEach((n=>s.add(n,"change",A))),L.active&&(y.translate.to(y.location.get()),y.animation.init(),y.slidesInView.init(),y.slideFocus.init(k),y.eventHandler.init(k),y.resizeHandler.init(k),y.slidesHandler.init(k),y.options.loop&&y.slideLooper.loop(),v.offsetParent&&S.length&&y.dragHandler.init(k),x=u.init(k,O)))}function A(n,t){const e=T();M(),I(d({startIndex:e},n),t),a.emit("reInit")}function M(){y.dragHandler.destroy(),y.eventStore.clear(),y.translate.clear(),y.slideLooper.clear(),y.resizeHandler.destroy(),y.slidesHandler.destroy(),y.slidesInView.destroy(),y.animation.destroy(),u.destroy(),s.clear()}function F(n,t,e){L.active&&!w&&(y.scrollBody.useBaseFriction().useDuration(!0===t?0:L.duration),y.scrollTo.index(n,e||0))}function T(){return y.index.get()}const k={canScrollNext:function(){return y.index.add(1).get()!==T()},canScrollPrev:function(){return y.index.add(-1).get()!==T()},containerNode:function(){return v},internalEngine:function(){return y},destroy:function(){w||(w=!0,s.clear(),M(),a.emit("destroy"),a.clear())},off:g,on:p,emit:m,plugins:function(){return x},previousScrollSnap:function(){return y.indexPrevious.get()},reInit:h,rootNode:function(){return n},scrollNext:function(n){F(y.index.add(1).get(),n,-1)},scrollPrev:function(n){F(y.index.add(-1).get(),n,1)},scrollProgress:function(){return y.scrollProgress.get(y.offsetLocation.get())},scrollSnapList:function(){return y.scrollSnapList},scrollTo:F,selectedScrollSnap:T,slideNodes:function(){return S},slidesInView:function(){return y.slidesInView.get()},slidesNotInView:function(){return y.slidesInView.get(!1)}};return I(t,e),setTimeout((()=>a.emit("init")),0),k}function C(t={},o=[]){const i=n.useRef(t),c=n.useRef(o),[u,s]=n.useState(),[a,d]=n.useState(),f=n.useCallback((()=>{u&&u.reInit(i.current,c.current)}),[u]);return n.useEffect((()=>{e(i.current,t)||(i.current=t,f())}),[t,f]),n.useEffect((()=>{(function(n,t){if(n.length!==t.length)return!1;const o=r(n),i=r(t);return o.every(((n,t)=>e(n,i[t])))})(c.current,o)||(c.current=o,f())}),[o,f]),n.useEffect((()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&a){B.globalOptions=C.globalOptions;const n=B(a,i.current,c.current);return s(n),()=>n.destroy()}s(void 0)}),[a,s]),[d,u]}return B.globalOptions=void 0,C.globalOptions=void 0,C}));
