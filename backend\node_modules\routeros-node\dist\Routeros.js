"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_net_1 = require("node:net");
const RouterosException_1 = __importDefault(require("./RouterosException"));
const helpers_1 = require("./helpers");
const node_tls_1 = __importDefault(require("node:tls"));
class Routeros {
    constructor({ host, port, user, password, timeout = 0, tlsOptions, }) {
        this.socket = new node_net_1.Socket();
        this.host = host;
        this.port = port;
        this.user = user;
        this.password = password;
        this.timeout = timeout * 1000;
        this.tlsOptions = tlsOptions;
    }
    writeWords(words) {
        for (const word of words) {
            this.socket.write((0, helpers_1.encodeString)(word));
        }
        this.socket.write((0, helpers_1.encodeString)(null));
    }
    login() {
        this.writeWords([
            "/login",
            `=name=${this.user}`,
            `=password=${this.password}`,
        ]);
    }
    connect() {
        return new Promise((resolve, reject) => {
            try {
                if (this.tlsOptions) {
                    this.socket = node_tls_1.default.connect(Object.assign({ host: this.host, port: this.port }, this.tlsOptions));
                }
                else {
                    this.socket = this.socket.connect({
                        host: this.host,
                        port: this.port,
                    });
                }
                this.socket.setTimeout(this.timeout);
            }
            catch (error) {
                return reject(new RouterosException_1.default(error.message));
            }
            const result = {
                type: "",
                message: "",
            };
            this.socket
                .on("data", (bufferData) => {
                (0, helpers_1.useSentenceParser)(bufferData, (line) => {
                    if (line === "!trap") {
                        result.type = "error";
                    }
                    else if (line.startsWith("=")) {
                        const [, key, value] = line.split("=");
                        result.message = value;
                    }
                    else if (line === "!done") {
                        if (result.type === "error") {
                            reject(new RouterosException_1.default(result.message));
                        }
                        else {
                            resolve(this);
                        }
                    }
                });
            })
                .on("error", (err) => {
                reject(new RouterosException_1.default(err.message));
            })
                .on("timeout", () => {
                this.socket.destroy(new Error("Socket timeout"));
            })
                .on("connect", () => {
                this.login();
            });
        });
    }
    write(queries) {
        return new Promise((resolve, reject) => {
            this.writeWords(queries);
            const result = {
                type: "",
                messages: [],
                data: [],
            };
            let isDone = false;
            this.socket.on("data", (bufferData) => {
                (0, helpers_1.useSentenceParser)(bufferData, (line) => {
                    if (line === "!re") {
                        result.data.push({});
                        result.type = "success";
                    }
                    else if (line === "!trap") {
                        result.type = "error";
                    }
                    else if (isDone && line.startsWith("=ret=")) {
                        const [, key, value] = line.split("=");
                        resolve([{ [key]: value }]);
                    }
                    else if (line.startsWith("=") && result.type !== "error") {
                        const [, key, value] = line.split("=");
                        result.data[result.data.length - 1][key] = value;
                    }
                    else if (line.startsWith("=") && result.type === "error") {
                        const [, key, value] = line.split("=");
                        result.messages.push(value);
                    }
                    else if (line === "!done") {
                        isDone = true;
                    }
                    else if (line === "" && isDone) {
                        if (result.type === "success") {
                            resolve(result.data);
                        }
                        else if (result.type === "") {
                            resolve(result.data);
                        }
                        else {
                            reject(new RouterosException_1.default(result.messages.join(". ")));
                        }
                    }
                });
            });
        });
    }
    destroy() {
        this.socket.destroy();
    }
}
exports.default = Routeros;
